# InfoVault DEV Environment Configuration
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# Environment Configuration
environment  = "dev"
aws_region   = "ap-southeast-1"
project_name = "infovault"

# S3 Bucket Configuration
s3_bucket_name   = ""   # Auto-generated: infovault-dev-storage-{random}
s3_force_destroy = true # Allow destruction in DEV environment

# S3 Versioning
enable_s3_versioning = true

# S3 Encryption
s3_encryption_algorithm = "AES256"
s3_kms_key_id           = null
s3_bucket_key_enabled   = true

# S3 Public Access Block (Security Best Practices)
s3_block_public_acls       = true
s3_block_public_policy     = true
s3_ignore_public_acls      = true
s3_restrict_public_buckets = true

# S3 Lifecycle Management
s3_lifecycle_enabled          = true
s3_transition_to_ia_days      = 30  # Move to Standard-IA after 30 days
s3_transition_to_glacier_days = 90  # Move to Glacier after 90 days
s3_expiration_days            = 365 # Delete after 1 year
s3_multipart_upload_days      = 7   # Clean up incomplete uploads after 7 days

# S3 Logging (Disabled for DEV)
s3_logging_enabled       = false
s3_logging_target_bucket = ""
s3_logging_target_prefix = "access-logs/"

# S3 Notifications (Disabled for DEV)
s3_notification_enabled = false

# =============================================================================
# NETWORKING CONFIGURATION
# =============================================================================

# Intranet Management Compartment VPC
intranet_management = {
  # VPC Configuration
  vpc_cidr                             = "**********/26"
  vpc_name                             = "infovault-dev-Intranet-management-compartment"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = false # No IGW for management VPC

  # Private Subnets (Management VPC is primarily private)
  private_subnets = {
    natmgt = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-natmgt-subnet"
    }
    natvpce_az1 = {
      cidr_block                      = "***********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-natvpce-subnet-az1"
    }
    natvpce_az2 = {
      cidr_block                      = "***********/28"
      availability_zone               = "us-east-1b"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-natvpce-subnet-az2"
    }
    natad = {
      cidr_block                      = "**********8/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-natad-subnet"
    }
  }

  # No public subnets for management VPC
  public_subnets = {}

  # Route Tables
  private_route_tables = {
    mgmt_main_rt = {
      name = "infovault-dev-mgmt-route-table"
    }
  }

  public_route_tables = {}

  # Route Table Associations
  private_subnet_route_associations = {
    natmgt_assoc = {
      subnet_key      = "natmgt"
      route_table_key = "mgmt_main_rt"
    }
    natvpce_az1_assoc = {
      subnet_key      = "natvpce_az1"
      route_table_key = "mgmt_main_rt"
    }
    natvpce_az2_assoc = {
      subnet_key      = "natvpce_az2"
      route_table_key = "mgmt_main_rt"
    }
    natad_assoc = {
      subnet_key      = "natad"
      route_table_key = "mgmt_main_rt"
    }
  }

  public_subnet_route_associations = {}

  # No NAT Gateways needed
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes (to be configured based on actual peering connections)
  vpc_peering_routes = {}

  # Security Groups (minimal configuration)
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs (minimal configuration)
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints (to be configured based on actual endpoints)
  vpc_endpoints = {}
}

# Internet Facing Compartment VPC
internet_facing = {
  # VPC Configuration
  vpc_cidr                             = "**********/27"
  vpc_name                             = "infovault-dev-internet-facing"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = true # IGW for internet access

  # Public Subnets for internet-facing resources
  public_subnets = {
    public_az1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-public-subnet-az1"
    }
    public_az2 = {
      cidr_block                      = "***********/28"
      availability_zone               = "us-east-1b"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-public-subnet-az2"
    }
  }

  # No private subnet for this VPC 
  private_subnets = {}

  # Route Tables
  public_route_tables = {
    public_rt = {
      name = "infovault-dev-pub-rt-netvpc"
    }
  }

  private_route_tables = {}

  # Route Table Associations
  public_subnet_route_associations = {
    public_az1_assoc = {
      subnet_key      = "public_az1"
      route_table_key = "public_rt"
    }
    public_az2_assoc = {
      subnet_key      = "public_az2"
      route_table_key = "public_rt"
    }
  }

  private_subnet_route_associations = {}

  # NAT Gateways (minimal configuration)
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups (not supported by current networking module)
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# Gen Facing Compartment VPC
gen_facing = {
  # VPC Configuration - Multiple CIDR blocks
  vpc_cidr                             = "*********/26"    # Primary CIDR
  secondary_cidr_blocks                = ["**********/22"] # Secondary CIDR (covers **********-************)
  vpc_name                             = "infovault-dev-gen-facing-compartment"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = true

  # Public Subnets (ALB only)
  public_subnets = {
    alb_subnet_az1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-alb-subnet-az1"
    }
  }

  # Private Subnets
  private_subnets = {
    gen_egress_az1 = {
      cidr_block                      = "*********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-gen-egress-az1"
    }
    gen_ingress_az1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-gen-ingress-az1"
    }
    smtp_subnet_dlz1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-smtp-subnet-dlz1"
    }
    app_subnet_az1 = {
      cidr_block                      = "**********/24"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-app-subnet-az1"
    }
    app_subnet_az2 = {
      cidr_block                      = "**********/24"
      availability_zone               = "us-east-1b"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-app-subnet-az2"
    }
    db_subnet_az1 = {
      cidr_block                      = "**********/26"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-db-subnet-az1"
    }
    db_subnet_az2 = {
      cidr_block                      = "***********/26"
      availability_zone               = "us-east-1b"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-db-subnet-az2"
    }
    gen_migration_az1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-gen-migration-az1"
    }
  }

  # Route Tables
  public_route_tables = {
    gen_public_rt = {
      name = "infovault-dev-gen-public-route-table"
    }
  }

  private_route_tables = {
    gen_private_rt = {
      name = "infovault-dev-gen-private-route-table"
    }
  }

  # Route Table Associations
  public_subnet_route_associations = {
    alb_subnet_az1_assoc = {
      subnet_key      = "alb_subnet_az1"
      route_table_key = "gen_public_rt"
    }
  }

  private_subnet_route_associations = {
    gen_egress_assoc = {
      subnet_key      = "gen_egress_az1"
      route_table_key = "gen_private_rt"
    }
    gen_ingress_assoc = {
      subnet_key      = "gen_ingress_az1"
      route_table_key = "gen_private_rt"
    }
    app_subnet_az1_assoc = {
      subnet_key      = "app_subnet_az1"
      route_table_key = "gen_private_rt"
    }
    app_subnet_az2_assoc = {
      subnet_key      = "app_subnet_az2"
      route_table_key = "gen_private_rt"
    }
    db_subnet_az1_assoc = {
      subnet_key      = "db_subnet_az1"
      route_table_key = "gen_private_rt"
    }
    db_subnet_az2_assoc = {
      subnet_key      = "db_subnet_az2"
      route_table_key = "gen_private_rt"
    }

    smtp_subnet_dlz1_assoc = {
      subnet_key      = "smtp_subnet_dlz1"
      route_table_key = "gen_private_rt"
    }
    gen_migration_assoc = {
      subnet_key      = "gen_migration_az1"
      route_table_key = "gen_private_rt"
    }
  }

  # NAT Gateways
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# ABLRHF Compartment VPC
ablrhf = {
  # VPC Configuration
  vpc_cidr                             = "*********/27"
  vpc_name                             = "infovault-dev-ablrhf-compartment"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = false # Private VPC

  # Private Subnets only
  private_subnets = {
    ablrhf_az1 = {
      cidr_block                      = "*********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-ablrhf-subnet-az1"
    }
  }

  public_subnets = {}

  # Route Tables
  private_route_tables = {
    ablrhf_rt = {
      name = "infovault-dev-ablrhf-route-table"
    }
  }

  public_route_tables = {}

  # Route Table Associations
  private_subnet_route_associations = {
    ablrhf_assoc = {
      subnet_key      = "ablrhf_az1"
      route_table_key = "ablrhf_rt"
    }
  }

  public_subnet_route_associations = {}

  # No NAT Gateways
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# Patching Compartment VPC
patching = {
  # VPC Configuration
  vpc_cidr                             = "**********/27"
  vpc_name                             = "infovault-dev-Patching-Compartment"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = false # Private VPC

  # Private Subnets only
  private_subnets = {
    patching_az1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "us-east-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "infovault-dev-patching-subnet-az1"
    }
  }

  public_subnets = {}

  # Route Tables
  private_route_tables = {
    patching_rt = {
      name = "infovault-dev-patching-route-table"
    }
  }

  public_route_tables = {}

  # Route Table Associations
  private_subnet_route_associations = {
    patching_assoc = {
      subnet_key      = "patching_az1"
      route_table_key = "patching_rt"
    }
  }

  public_subnet_route_associations = {}

  # No NAT Gateways
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# Common Tags
common_tags = {
  Project     = "InfoVault"
  Environment = "dev"
  ManagedBy   = "Terraform"
  UseCase     = "UC4-Setup-Manage-AWS-Infrastructure-DEV"
  Owner       = "InfoVault-IAC-Team"
  CostCenter  = "InfoVault-Development"
  Backup      = "Required"
  Monitoring  = "Enabled"
}

# =============================================================================
# EKS CONFIGURATION VALUES
# =============================================================================

# EKS Cluster Configuration
cluster_name         = "infovault-dev-eks-cluster-v130"
cluster_version      = "1.32"
cluster_role_name    = "eks-cluster-role-infovault-dev-eks-cluster-v130"
node_group_role_name = "eks-nodegroup-role-infovault-dev-eks-cluster-v130"

# VPC Configuration for EKS (infovault-dev-gen-facing-compartment VPC)
subnet_ids                 = ["subnet-087a114ef79afa85f", "subnet-00a5a802525196bba"]
cluster_security_group_ids = []
endpoint_private_access    = true
endpoint_public_access     = true
public_access_cidrs        = ["0.0.0.0/0"]
cluster_log_types          = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
kms_key_arn                = null

# Node Group Configuration
node_group_name           = "v130-node-group-3"
node_group_subnet_ids     = ["subnet-087a114ef79afa85f", "subnet-00a5a802525196bba"]
node_group_instance_types = ["t3.medium"]
node_group_ami_type       = "AL2_x86_64"
node_group_capacity_type  = "ON_DEMAND"
node_group_disk_size      = 20
node_group_desired_size   = 2
node_group_max_size       = 4
node_group_min_size       = 1
node_group_labels         = { "environment" : "dev", "role" : "application" }

# EKS Addon Versions (compatible with Kubernetes 1.32)
addon_amazon_cloudwatch_observability_version = null
addon_aws_ebs_csi_driver_version              = null
addon_aws_guardduty_agent_version             = null
addon_coredns_version                         = "v1.11.4-eksbuild.14"
addon_eks_pod_identity_agent_version          = null
addon_kube_proxy_version                      = null
addon_vpc_cni_version                         = "v1.19.5-eksbuild.3"

# =============================================================================
# EC2 INSTANCE CONFIGURATIONS
# =============================================================================

# infovault-dev-gitlab-runner configuration
infovault_dev_gitlab_runner_name                      = "infovault-dev-gitlab-runner"
infovault_dev_gitlab_runner_ami_id                    = "*********************"
infovault_dev_gitlab_runner_instance_type             = "m5.xlarge"
infovault_dev_gitlab_runner_key_name                  = null
infovault_dev_gitlab_runner_security_group_ids        = ["sg-04d4b09b1d60de68f"]
infovault_dev_gitlab_runner_subnet_id                 = "subnet-06742da032d3c33ed"
infovault_dev_gitlab_runner_iam_instance_profile_name = "infovault-dev-ec2-profile"
infovault_dev_gitlab_runner_availability_zone         = "us-east-1a"
infovault_dev_gitlab_runner_monitoring_enabled        = false
infovault_dev_gitlab_runner_ebs_optimized             = false
infovault_dev_gitlab_runner_source_dest_check         = true
infovault_dev_gitlab_runner_private_ip_address        = "**********"
infovault_dev_gitlab_runner_root_block_device         = null
infovault_dev_gitlab_runner_ebs_block_devices         = []
infovault_dev_gitlab_runner_user_data                 = <<-EOF
#!/bin/bash
yum update -y
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent
EOF

# infovault-dev-linux-tooling-server configuration
infovault_dev_linux_tooling_server_name                      = "infovault-dev-linux-tooling-server"
infovault_dev_linux_tooling_server_ami_id                    = "*********************"
infovault_dev_linux_tooling_server_instance_type             = "t3.medium"
infovault_dev_linux_tooling_server_key_name                  = null
infovault_dev_linux_tooling_server_security_group_ids        = ["sg-04d4b09b1d60de68f"]
infovault_dev_linux_tooling_server_subnet_id                 = "subnet-06742da032d3c33ed"
infovault_dev_linux_tooling_server_iam_instance_profile_name = "infovault-dev-ec2-profile"
infovault_dev_linux_tooling_server_availability_zone         = "us-east-1a"
infovault_dev_linux_tooling_server_monitoring_enabled        = false
infovault_dev_linux_tooling_server_ebs_optimized             = false
infovault_dev_linux_tooling_server_source_dest_check         = true
infovault_dev_linux_tooling_server_private_ip_address        = "**********"
infovault_dev_linux_tooling_server_root_block_device         = null
infovault_dev_linux_tooling_server_ebs_block_devices         = []
infovault_dev_linux_tooling_server_user_data                 = <<-EOF
#!/bin/bash
yum update -y
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent
EOF

# infovault-dev-win-tooling-server configuration
mgmt_newgenadm_win_tooling_01_name                      = "infovault-dev-win-tooling-server"
mgmt_newgenadm_win_tooling_01_ami_id                    = "ami-0db3480be03d8d01c"
mgmt_newgenadm_win_tooling_01_instance_type             = "t3.xlarge"
mgmt_newgenadm_win_tooling_01_key_name                  = null
mgmt_newgenadm_win_tooling_01_security_group_ids        = ["sg-04d4b09b1d60de68f"]
mgmt_newgenadm_win_tooling_01_subnet_id                 = "subnet-06742da032d3c33ed"
mgmt_newgenadm_win_tooling_01_iam_instance_profile_name = "infovault-dev-ec2-profile"
mgmt_newgenadm_win_tooling_01_availability_zone         = "us-east-1a"
mgmt_newgenadm_win_tooling_01_monitoring_enabled        = false
mgmt_newgenadm_win_tooling_01_ebs_optimized             = false
mgmt_newgenadm_win_tooling_01_source_dest_check         = true
mgmt_newgenadm_win_tooling_01_private_ip_address        = "**********"
mgmt_newgenadm_win_tooling_01_root_block_device         = null
mgmt_newgenadm_win_tooling_01_ebs_block_devices         = []
mgmt_newgenadm_win_tooling_01_user_data                 = <<-EOF
<powershell>
# Enable and start SSM Agent
Set-Service -Name "AmazonSSMAgent" -StartupType Automatic
Start-Service -Name "AmazonSSMAgent"
Get-Service -Name "AmazonSSMAgent"
</powershell>
EOF

# dev-management-server-avm150-new configuration
dev_management_server_avm150_new_name                      = "dev-management-server-avm150-new"
dev_management_server_avm150_new_ami_id                    = "ami-02f7b163d79aae0cb"
dev_management_server_avm150_new_instance_type             = "c6a.xlarge"
dev_management_server_avm150_new_key_name                  = null
dev_management_server_avm150_new_security_group_ids        = ["sg-0f2fdd192120dbf17"]
dev_management_server_avm150_new_subnet_id                 = "subnet-06742da032d3c33ed"
dev_management_server_avm150_new_iam_instance_profile_name = "dev-dam-management-servers-profile"
dev_management_server_avm150_new_availability_zone         = "us-east-1a"
dev_management_server_avm150_new_monitoring_enabled        = false
dev_management_server_avm150_new_ebs_optimized             = false
dev_management_server_avm150_new_source_dest_check         = true
dev_management_server_avm150_new_private_ip_address        = "***********"
dev_management_server_avm150_new_root_block_device         = null
dev_management_server_avm150_new_ebs_block_devices         = []
dev_management_server_avm150_new_user_data                = <<-EOF
#!/bin/bash
yum update -y
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent
EOF

# dev-dra-admin-server-new configuration
dev_dra_admin_server_new_name                      = "infovault-dev-draadmin-server"
dev_dra_admin_server_new_ami_id                    = "*********************"
dev_dra_admin_server_new_instance_type             = "m5.xlarge"
dev_dra_admin_server_new_key_name                  = null
dev_dra_admin_server_new_security_group_ids        = ["sg-04d4b09b1d60de68f"]
dev_dra_admin_server_new_subnet_id                 = "subnet-06742da032d3c33ed"
dev_dra_admin_server_new_iam_instance_profile_name = "infovault-dev-ec2-profile"
dev_dra_admin_server_new_availability_zone         = "us-east-1a"
dev_dra_admin_server_new_monitoring_enabled        = false
dev_dra_admin_server_new_ebs_optimized             = false
dev_dra_admin_server_new_source_dest_check         = true
dev_dra_admin_server_new_private_ip_address        = "**********"
dev_dra_admin_server_new_root_block_device         = null
dev_dra_admin_server_new_ebs_block_devices         = []
dev_dra_admin_server_new_user_data                 = <<-EOF
#!/bin/bash
yum update -y
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent
EOF

# dev-dra-analytics-server-new configuration
dev_dra_analytics_server_new_name                      = "infovault-dev-draanalytics-server"
dev_dra_analytics_server_new_ami_id                    = "*********************"
dev_dra_analytics_server_new_instance_type             = "m5.xlarge"
dev_dra_analytics_server_new_key_name                  = null
dev_dra_analytics_server_new_security_group_ids        = ["sg-04d4b09b1d60de68f"]
dev_dra_analytics_server_new_subnet_id                 = "subnet-06742da032d3c33ed"
dev_dra_analytics_server_new_iam_instance_profile_name = "infovault-dev-ec2-profile"
dev_dra_analytics_server_new_availability_zone         = "us-east-1a"
dev_dra_analytics_server_new_monitoring_enabled        = false
dev_dra_analytics_server_new_ebs_optimized             = false
dev_dra_analytics_server_new_source_dest_check         = true
dev_dra_analytics_server_new_private_ip_address        = "***********"
dev_dra_analytics_server_new_root_block_device         = null
dev_dra_analytics_server_new_ebs_block_devices         = []
dev_dra_analytics_server_new_user_data                 = <<-EOF
#!/bin/bash
yum update -y
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent
systemctl status amazon-ssm-agent
EOF

# dev-management-server-avm150-imperva configuration
dev_management_server_avm150_imperva_name                      = "dev-management-server-avm150-imperva"
dev_management_server_avm150_imperva_ami_id                    = "ami-0d23603cba346ca65"
dev_management_server_avm150_imperva_instance_type             = "c6a.xlarge"
dev_management_server_avm150_imperva_key_name                  = "dam-key"
dev_management_server_avm150_imperva_security_group_ids        = ["sg-0f2fdd192120dbf17"]
dev_management_server_avm150_imperva_subnet_id                 = "subnet-06742da032d3c33ed"
dev_management_server_avm150_imperva_iam_instance_profile_name = "dev-dam-management-servers-profile"
dev_management_server_avm150_imperva_availability_zone         = "us-east-1a"
dev_management_server_avm150_imperva_monitoring_enabled        = false
dev_management_server_avm150_imperva_ebs_optimized             = true
dev_management_server_avm150_imperva_source_dest_check         = true
dev_management_server_avm150_imperva_private_ip_address        = "**********"
dev_management_server_avm150_imperva_root_block_device         = null
dev_management_server_avm150_imperva_ebs_block_devices         = []

# ad-tooling-windows-02 configuration
ad_tooling_windows_02_name                      = "infovault-dev-ad-tooling-server"
ad_tooling_windows_02_ami_id                    = "ami-0db3480be03d8d01c"
ad_tooling_windows_02_instance_type             = "t3.xlarge"
ad_tooling_windows_02_key_name                  = null
ad_tooling_windows_02_security_group_ids        = ["sg-04d4b09b1d60de68f"]
ad_tooling_windows_02_subnet_id                 = "subnet-06742da032d3c33ed"
ad_tooling_windows_02_iam_instance_profile_name = "infovault-dev-ec2-profile"
ad_tooling_windows_02_availability_zone         = "us-east-1a"
ad_tooling_windows_02_monitoring_enabled        = false
ad_tooling_windows_02_ebs_optimized             = false
ad_tooling_windows_02_source_dest_check         = true
ad_tooling_windows_02_private_ip_address        = "**********"
ad_tooling_windows_02_root_block_device         = null
ad_tooling_windows_02_ebs_block_devices         = []
ad_tooling_windows_02_user_data                 = <<-EOF
<powershell>
# Enable and start SSM Agent
Set-Service -Name "AmazonSSMAgent" -StartupType Automatic
Start-Service -Name "AmazonSSMAgent"
Get-Service -Name "AmazonSSMAgent"
</powershell>
EOF

# infovault-dev-marvin-ai-02 configuration
infovault_dev_marvin_ai_02_name                      = "infovault-dev-marvin-ai-02"
infovault_dev_marvin_ai_02_ami_id                    = "ami-032ad2fe3dbb3e065"
infovault_dev_marvin_ai_02_instance_type             = "g4dn.xlarge"
infovault_dev_marvin_ai_02_key_name                  = "marvinAI"
infovault_dev_marvin_ai_02_security_group_ids        = ["sg-0711fefc6398ca8fe"]
infovault_dev_marvin_ai_02_subnet_id                 = "subnet-023cafbedbb31d13e"
infovault_dev_marvin_ai_02_iam_instance_profile_name = "infovault-dev-ec2-profile"
infovault_dev_marvin_ai_02_availability_zone         = "ap-southeast-1a"
infovault_dev_marvin_ai_02_monitoring_enabled        = false
infovault_dev_marvin_ai_02_ebs_optimized             = true
infovault_dev_marvin_ai_02_source_dest_check         = true
infovault_dev_marvin_ai_02_private_ip_address        = null
infovault_dev_marvin_ai_02_root_block_device         = null
infovault_dev_marvin_ai_02_ebs_block_devices         = []
infovault_dev_marvin_ai_02_user_data                 = <<-EOF
#!/bin/bash
# Update system
dnf update -y

# Install SSM agent (if not already installed)
dnf install -y amazon-ssm-agent

# Enable and start SSM Agent
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

# Verify SSM agent is running
systemctl status amazon-ssm-agent

# Log SSM agent status
echo "SSM Agent installation completed at $(date)" >> /var/log/ssm-setup.log
EOF

# Internet Facing Security Groups Configuration
internet_facing_security_groups = {
  proxy_egress = {
    name        = "infovault-pxy-eg-01"
    description = "InfoVault Internet Facing Proxy Egress Security Group"
  }
  nfw_egress = {
    name        = "infovault-nfw-eg-01"
    description = "InfoVault Internet Facing Network Firewall Egress Security Group"
  }
}

# Internet Facing Security Group Ingress Rules
internet_facing_sg_ingress_rules = {
  proxy_egress_https = {
    security_group_key = "proxy_egress"
    from_port          = 443
    to_port            = 443
    protocol           = "tcp"
    cidr_blocks        = ["0.0.0.0/0"]
    description        = "HTTPS inbound traffic"
  }
  proxy_egress_http = {
    security_group_key = "proxy_egress"
    from_port          = 80
    to_port            = 80
    protocol           = "tcp"
    cidr_blocks        = ["0.0.0.0/0"]
    description        = "HTTP inbound traffic"
  }
  nfw_egress_internal_10 = {
    security_group_key = "nfw_egress"
    from_port          = 0
    to_port            = 65535
    protocol           = "tcp"
    cidr_blocks        = ["10.0.0.0/8"]
    description        = "All TCP traffic from 10.0.0.0/8 network"
  }
  nfw_egress_internal_100 = {
    security_group_key = "nfw_egress"
    from_port          = 0
    to_port            = 65535
    protocol           = "tcp"
    cidr_blocks        = ["**********/10"]
    description        = "All TCP traffic from **********/10 network"
  }
}

# Internet Facing Security Group Egress Rules
internet_facing_sg_egress_rules = {
  proxy_egress_all = {
    security_group_key = "proxy_egress"
    from_port          = 0
    to_port            = 65535
    protocol           = "-1"
    cidr_blocks        = ["0.0.0.0/0"]
    description        = "All outbound traffic"
  }
  nfw_egress_all = {
    security_group_key = "nfw_egress"
    from_port          = 0
    to_port            = 65535
    protocol           = "-1"
    cidr_blocks        = ["0.0.0.0/0"]
    description        = "All outbound traffic"
  }
}
